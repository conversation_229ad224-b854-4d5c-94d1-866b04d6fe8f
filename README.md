# NotionNext

<div align="center">

**🚀 一个使用 Next.js + Notion API 实现的现代化博客系统**

*为 Notion 用户和内容创作者量身打造的静态博客解决方案*

</div>

## 📖 帮助文档

- 📚 [完整使用手册](https://docs.tangly1024.com/) - 详细的安装和配置指南
- 🎯 [快速开始教程](https://docs.tangly1024.com/article/notion-tutorial) - 从零开始搭建你的博客
- 💡 [常见问题解答](https://docs.tangly1024.com/faq) - 解决常见问题

## ⚖️ 使用声明

> **重要提示**: 本项目为开源免费项目，仅供个人学习和非商业用途使用。
>
> - ✅ **允许**: 个人学习、研究、非商业使用
> - ❌ **禁止**: 商业售卖、收费服务、未授权的商业用途
> - 📝 **转载要求**: 转载时请保留作者信息并注明来源
> - ⚠️ **防诈骗**: 仅认可官方授权的付费咨询服务，谨防诈骗

## 🌟 关于 Notion

Notion 是一个强大的生产力工具，集文档编写、笔记管理、知识库构建、项目规划于一体，配合 AI 技术，能够显著提升工作效率。NotionNext 让你能够将 Notion 中的内容轻松转换为美观的博客网站。

<div align="center">

<p>
  <a aria-label="GitHub commit activity" href="https://github.com/tangly1024/NotionNext/commits/main" title="GitHub commit activity">
    <img src="https://img.shields.io/github/commit-activity/m/tangly1024/NotionNext?style=for-the-badge"/>
  </a>
  <a aria-label="GitHub contributors" href="https://github.com/tangly1024/NotionNext/graphs/contributors" title="GitHub contributors">
    <img src="https://img.shields.io/github/contributors/tangly1024/NotionNext?color=orange&style=for-the-badge"/>
  </a>
  <a aria-label="Build status" href="#" title="Build status">
    <img src="https://img.shields.io/github/deployments/tangly1024/NotionNext/Production?logo=Vercel&style=for-the-badge"/>
  </a>
  <a aria-label="Powered by Vercel" href="https://vercel.com?utm_source=Craigary&utm_campaign=oss" title="Powered by Vercel">
    <img src="https://www.datocms-assets.com/31049/1618983297-powered-by-vercel.svg" height="28"/>
  </a>
</p>

[中文文档](./README.md) | [English Documentation](./README_EN.md)

</div>

---

## 🎯 项目简介

NotionNext 是一个基于 **Next.js** 和 **Notion API** 构建的现代化静态博客系统，专为 Notion 用户和内容创作者设计。它能够将你在 Notion 中创建的内容自动转换为美观、高性能的博客网站。

### ✨ 核心特性

- 🎨 **20+ 精美主题** - 从简约到华丽，总有一款适合你
- 🚀 **极速性能** - 基于 Next.js 的静态生成，加载速度极快
- 📱 **响应式设计** - 完美适配桌面端、平板和移动设备
- 🔧 **高度可定制** - 丰富的配置选项，满足个性化需求
- 🌍 **多语言支持** - 支持中文、英文、日文等多种语言
- 🔍 **SEO 优化** - 内置 SEO 最佳实践，提升搜索引擎排名
- 💬 **多评论系统** - 支持 Twikoo、Giscus、Gitalk 等多种评论系统
- 📊 **数据统计** - 集成多种统计分析工具
- 🎭 **动效美化** - 丰富的动画效果和交互体验
- 🤖 **AI 增强** - 支持 AI 摘要、智能客服等功能

### 🚀 部署方案

- ☁️ **Vercel** (推荐) - 一键部署，自动 CI/CD
- 🌐 **Netlify** - 静态站点托管
- 📄 **GitHub Pages** - 免费托管方案
- 🐳 **Docker** - 容器化部署
- 💻 **自托管** - 完全控制的部署方案

## 🎨 主题预览

> 🌐 **在线演示**: [https://preview.tangly1024.com/](https://preview.tangly1024.com/)
>
> 💡 **提示**: 点击左下角挂件可以实时切换主题体验不同风格

<div align="center">

### 🔥 热门主题展示

| Next 主题 | Medium 主题 | Hexo 主题 | Fukasawa 主题 |
|:---:|:---:|:---:|:---:|
| <img src='./docs/theme-next.png' width='280'/><br/>📖 [预览 NEXT](https://preview.tangly1024.com/?theme=next) | <img src='./docs/theme-medium.png' width='280'/><br/>✍️ [预览 MEDIUM](https://preview.tangly1024.com/?theme=medium) | <img src='./docs/theme-hexo.png' width='280'/><br/>🎯 [预览 HEXO](https://preview.tangly1024.com/?theme=hexo) | <img src='./docs/theme-fukasawa.png' width='280'/><br/>🎨 [预览 FUKASAWA](https://preview.tangly1024.com/?theme=fukasawa) |
| 经典博客风格 | 现代简约设计 | 传统 Hexo 风格 | 日式极简美学 |

</div>

### 🎭 更多主题

我们提供 **20+ 个精心设计的主题**，包括：

- 🏢 **商业主题**: Commerce, Landing
- 🎮 **娱乐主题**: Game, Movie
- 📚 **文档主题**: GitBook, Simple
- 📸 **展示主题**: Photo, Magzine
- 🎨 **创意主题**: Heo, Matery, Plog

> 💡 **找不到心仪的主题？** 欢迎 [贡献你的主题设计](/CONTRIBUTING.md) 或 [提交主题需求](https://github.com/tangly1024/NotionNext/issues)！

## 🚀 快速开始

### 📋 前置要求

- 📝 **Notion 账户** - [注册 Notion](https://www.notion.so/)
- 🌐 **Vercel 账户** - [注册 Vercel](https://vercel.com/) (推荐部署平台)
- 💻 **Node.js** - 版本 16.x 或更高 (本地开发需要)

### ⚡ 一键部署 (推荐)

1. **复制 Notion 模板**
   ```
   📋 复制模板: https://www.notion.so/tanghh/02ab3b8678004aa69e9e415905ef32a5
   ```

2. **获取 Notion 页面 ID**
   ```
   📝 从 Notion 页面 URL 中复制页面 ID
   例如: https://notion.so/your-page-id
   ```

3. **一键部署到 Vercel**

   [![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/tangly1024/NotionNext&env=NOTION_PAGE_ID&envDescription=Notion%20Page%20ID&envLink=https://docs.tangly1024.com/)

4. **配置环境变量**
   ```bash
   NOTION_PAGE_ID=你的Notion页面ID
   NEXT_PUBLIC_THEME=heo  # 选择主题
   ```

### 💻 本地开发

```bash
# 1. 克隆项目
git clone https://github.com/tangly1024/NotionNext.git
cd NotionNext

# 2. 安装依赖
npm install
# 或使用 yarn/pnpm
yarn install
pnpm install

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件，填入你的配置

# 4. 启动开发服务器
npm run dev
# 访问 http://localhost:3000
```

## 🛠️ 配置指南

### 📝 基础配置

在 `blog.config.js` 中进行基本配置：

```javascript
const BLOG = {
  NOTION_PAGE_ID: '你的Notion页面ID',
  THEME: 'heo',                    // 主题名称
  LANG: 'zh-CN',                   // 语言设置
  AUTHOR: '你的名字',               // 作者名称
  BIO: '你的简介',                 // 个人简介
  LINK: 'https://你的域名.com',     // 网站地址
  // ... 更多配置选项
}
```

### 🎨 主题配置

每个主题都有独立的配置文件，位于 `themes/主题名/config.js`：

```javascript
// 主题特定配置
const CONFIG = {
  // 导航栏配置
  NAV_TYPE: 'normal',
  // 侧边栏配置
  RIGHT_BAR: true,
  // 更多主题配置...
}
```

### 💬 评论系统配置

支持多种评论系统，在 `conf/comment.config.js` 中配置：

```javascript
// Twikoo 评论配置
COMMENT_TWIKOO_ENV_ID: '你的环境ID',

// Giscus 评论配置
COMMENT_GISCUS_REPO: '你的仓库名',
COMMENT_GISCUS_REPO_ID: '仓库ID',

// 更多评论系统配置...
```

## 🎯 致谢

### 🙏 特别感谢

感谢 [Craig Hart](https://github.com/craigary) 发起的 [Nobelium](https://github.com/craigary/nobelium) 项目，为 NotionNext 提供了重要的灵感和基础。

<div align="center">
<a href="https://github.com/craigary" title="Craig Hart">
  <img src="https://avatars.githubusercontent.com/u/10571717" width="64px;" alt="Craig Hart"/>
  <br/>
  <strong>Craig Hart</strong>
</a>
</div>

### 👥 贡献者

<table>
  <tr align="left">
  <td align="center">
  <a href="https://github.com/tangly1024" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/15920488" width="64px;"alt="tangly1024"/><br/><sub><b>tangly1024</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=tangly1024" title="Owner" >🎫 🔧 🐛</a>
  </td> 
    
  <td align="center">
    <a href="https://github.com/uWayLu"  style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/21689326" width="64px;" alt="uWayLu"/><br/><sub><b>uWayLu</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=uWayLu" title="uWayLu" >🔧 🐛</a>
  </td>
    
  <td align="center">
    <a href="https://github.com/txs" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/554329" width="64px;" alt="txs"/><br/><sub><b>txs</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=txs" title="txs" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/yuzhanglong" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/56540811" width="64px;" alt="yuzhanglong"/><br/><sub><b>yuzhanglong</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=yuzhanglong" title="yuzhanglong" >🔧 🐛</a>
  </td> 
    
  <td align="center">
    <a href="https://github.com/Hscpro" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/13926044" width="64px;" alt="Hscpro"/><br/><sub><b>Hscpro</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=Hscpro" title="Hscpro" >🔧 🐛</a>
  </td> 
    
  <td align="center">
    <a href="https://github.com/JensonMiao" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/46488783" width="64px;" alt="JensonMiao"/><br/><sub><b>JensonMiao</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=JensonMiao" title="JensonMiao" >🔧 🐛</a>
  </td> 
  
  <td align="center">
    <a href="https://github.com/haixin1225" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/28828438" width="64px;"  alt="haixin1225"/><br/><sub><b>haixin1225</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=haixin1225" title="haixin1225" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/mouyase" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/7844572" width="64px;"  alt="mouyase"/><br/><sub><b>mouyase</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=mouyase" title="mouyase" >🔧 🐛</a>
  </td>
  
  <td align="center">
    <a href="https://github.com/qfdk" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/2404478" width="64px;"  alt="qfdk"/><br/><sub><b>qfdk</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=qfdk" title="qfdk" >🔧 🐛</a>
  </td>
  
  <td align="center">
    <a href="https://github.com/ifyz" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/118271360" width="64px;"  alt="ifyz"/><br/><sub><b>ifyz</b></sub></a><br><a href="https://github.com/tangly1024/NotionNext/commits?author=ifyz" title="ifyz" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/liqun98" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/71168966" width="64px;"  alt="Liqun Zhao"/><br/><sub><b>Liqun Zhao</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=liqun98" title="liqun98" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/Ylarod" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/30978685" width="64px;"  alt="Ylarod"/><br/><sub><b>Ylarod</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=Ylarod" title="Ylarod" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/lifeafter619" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/65111206" width="64px;"  alt="Etherrreal."/><br/><sub><b>Etherrreal.</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=lifeafter619" title="Etherrreal." >🔧 🐛</a>
  </td>
  
  <td align="center">
  <a href="https://github.com/ykxkykx" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/17985993" width="64px;"  alt="Joshua Astray"/><br/><sub><b>Joshua Astray</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=ykxkykx" title="ykxkykx" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/Vixcity" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/57704177" width="64px;"  alt="Vixcity"/><br/><sub><b>Vixcity</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=Vixcity" title="Vixcity" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/ipatpat" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/39089551" width="64px;"  alt="ipatpat"/><br/><sub><b>ipatpat</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=ipatpat" title="ipatpat" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/xloong" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/8479955" width="64px;"  alt="xloong"/><br/><sub><b>xloong</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=xloong" title="xloong" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/expoli" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/31023767" width="64px;"  alt="expoli"/><br/><sub><b>expoli</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=expoli" title="expoli" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/SuperHuangXu" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/14816052" width="64px;"  alt="SuperHuangXu"/><br/><sub><b>bUBBLE</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=SuperHuangXu" title="SuperHuangXu" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/Pylogmon" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/59004461" width="64px;"  alt="Pylogmon"/><br/><sub><b>派了个萌 </b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=Pylogmon" title="Pylogmon" >🔧 🐛</a>
  </td>
  
  <td align="center">
    <a href="https://github.com/SkysCrystal" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/49473463" width="64px;"  alt="SkysCrystal"/><br/><sub><b>Simon Shi</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=SkysCrystal" title="SkysCrystal" >🔧 🐛</a>
  </td>
  
  <td align="center">
    <a href="https://github.com/siygle" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/173408" width="64px;"  alt="S.Y. Lee"/><br/><sub><b>S.Y. Lee</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=siygle" title="siygle" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/fighting-bug" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/56441589" width="64px;"  alt="fighting-buf"/><br/><sub><b>fighting-buf</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=fighting-bug" title="fighting-buf" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/cliouo" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/71540889" width="64px;"  alt="cliouo"/><br/><sub><b>cliouo</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=cliouo" title="cliouo" >🔧 🐛</a>
  </td>
  
  <td align="center">
    <a href="https://github.com/sudeakq" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/102792219" width="64px;"  alt="Sude Akgün"/><br/><sub><b>Sude Akgün</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=sudeakq" title="sudeakq" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/fgprodigal" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/769910" width="64px;"  alt="Ray"/><br/><sub><b>Ray</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=fgprodigal" title="Ray" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/hongzzz" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/25585061" width="64px;"  alt="Hongzzz"/><br/><sub><b>Hongzzz</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=hongzzz" title="hongzzz" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/RedhairHambagu" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/129669334" width="64px;"  alt="RedhairHambagu"/><br/><sub><b>RedhairHambagu</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=RedhairHambagu" title="RedhairHambagu" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/Allengl" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/58612763" width="64px;"  alt="Allen"/><br/><sub><b>Allen</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=Allengl" title="Allengl" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/zdf1230" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/5999425" width="64px;"  alt="zdf1230"/><br/><sub><b>zdf</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=zdf1230" title="zdf1230" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/emengweb" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/31469739" width="64px;"  alt="emengweb"/><br/><sub><b>emengweb</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=emengweb" title="emengweb" >🔧 🐛</a>
  </td>

  <td align="center">
    <a href="https://github.com/kitety" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/22906933" width="64px;"  alt="kitety"/><br/><sub><b>kitety</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=kitety" title="kitety" >🔧 🐛</a>
  </td>

 <td align="center">
    <a href="https://github.com/jxpeng98" style="display:inline-block;width:80px"><img src="https://avatars.githubusercontent.com/u/83734772" width="64px;"  alt=" Jiaxin Peng"/><br/><sub><b> Jiaxin Peng</b></sub></a><br/><a href="https://github.com/tangly1024/NotionNext/commits?author=jxpeng98" title="jxpeng98" >🔧 🐛</a>
  </td>

</tr>
</table>

## 🛠️ 技术栈

<div align="center">

### 核心技术

| 技术 | 描述 | 版本 |
|:---:|:---:|:---:|
| [Next.js](https://nextjs.org) | React 全栈框架 | 14.2.4 |
| [React](https://reactjs.org) | 用户界面库 | 18.2.0 |
| [Tailwind CSS](https://tailwindcss.com) | 原子化 CSS 框架 | 3.3.2 |
| [TypeScript](https://typescriptlang.org) | 类型安全的 JavaScript | 5.6.2 |

### 核心依赖

| 功能 | 技术选型 |
|:---:|:---:|
| **内容渲染** | [react-notion-x](https://github.com/NotionX/react-notion-x) |
| **数据获取** | [notion-client](https://github.com/NotionX/react-notion-x/tree/master/packages/notion-client) |
| **样式处理** | [PostCSS](https://postcss.org/) + [Autoprefixer](https://autoprefixer.github.io/) |
| **图标库** | [Font Awesome](https://fontawesome.com/) |
| **部署平台** | [Vercel](https://vercel.com/) |

### 评论系统支持

| 评论系统 | 特点 | 推荐指数 |
|:---:|:---:|:---:|
| [Twikoo](https://twikoo.js.org/) | 简洁、安全、免费 | ⭐⭐⭐⭐⭐ |
| [Giscus](https://giscus.app/zh-CN) | 基于 GitHub Discussions | ⭐⭐⭐⭐⭐ |
| [Gitalk](https://gitalk.github.io) | 基于 GitHub Issues | ⭐⭐⭐⭐ |
| [Cusdis](https://cusdis.com) | 轻量级、隐私友好 | ⭐⭐⭐⭐ |
| [Utterances](https://utteranc.es) | 基于 GitHub Issues | ⭐⭐⭐ |

</div>

## 🤝 参与贡献

我们欢迎所有形式的贡献！无论是新功能、bug 修复、文档改进还是主题设计。

### 🎨 贡献主题

1. 复制 `themes/example` 文件夹
2. 重命名为你的主题名称
3. 自定义样式和组件
4. 提交 Pull Request

### 🌍 贡献翻译

1. 复制 `lib/lang/en-US.js` 文件
2. 重命名为对应语言代码（如 `es-ES.js`）
3. 翻译所有文本内容
4. 在 `lib/lang.js` 中添加语言配置

### 📝 贡献文档

- 改进现有文档
- 添加使用教程
- 分享最佳实践
- 报告和修复文档错误

详细贡献指南请查看 [CONTRIBUTING.md](./CONTRIBUTING.md)

## 🔗 相关项目

- 📝 [Elog](https://github.com/LetTTGACO/elog) - Markdown 批量导出工具，支持多平台博客解决方案
- 🎨 [Notion Avatar](https://notion-avatar.vercel.app/) - Notion 风格头像生成器
- 📚 [Notion Blog](https://github.com/ijjk/notion-blog) - 另一个基于 Notion 的博客方案

## 📄 开源协议

本项目基于 [MIT License](./LICENSE) 开源协议。

## 📈 项目统计

<div align="center">

[![Star History Chart](https://api.star-history.com/svg?repos=tangly1024/NotionNext&type=Date)](https://star-history.com/#tangly1024/NotionNext&Date)

**🌟 如果这个项目对你有帮助，请给我们一个 Star！**

</div>

---

<div align="center">

**💖 感谢所有贡献者和用户的支持！**

Made with ❤️ by [tangly1024](https://github.com/tangly1024) and [contributors](https://github.com/tangly1024/NotionNext/graphs/contributors)

</div>
