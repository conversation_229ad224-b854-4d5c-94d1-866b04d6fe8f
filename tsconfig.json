{"compilerOptions": {"baseUrl": ".", "paths": {"@/blog.config": ["./blog.config"], "@/components/*": ["components/*"], "@/hooks/*": ["hooks/*"], "@/themes/*": ["themes/*"], "@/pages/*": ["pages/*"], "@/data/*": ["data/*"], "@/lib/*": ["lib/*"], "@/styles/*": ["styles/*"]}, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "incremental": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve"}, "include": ["next-env.d.ts", "**/*.js", "**/*.ts", "**/*.tsx", "**/*.jsx", ".eslintrc.js"], "exclude": ["node_modules"]}